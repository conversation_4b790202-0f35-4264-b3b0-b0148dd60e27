import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { formatMusicData } from '@/api/music'

export const useMusicStore = defineStore('music', () => {
  // 当前播放的歌曲
  const currentSong = ref(null)
  
  // 播放状态
  const isPlaying = ref(false)
  const isPaused = ref(false)
  const isLoading = ref(false)
  
  // 播放进度
  const currentTime = ref(0)
  const duration = ref(0)
  const volume = ref(0.8)
  
  // 播放模式 (0: 顺序播放, 1: 单曲循环, 2: 随机播放)
  const playMode = ref(0)
  
  // 播放列表
  const playlist = ref([])
  const currentIndex = ref(0)
  
  // 搜索相关
  const searchResults = ref([]) // 当前显示的搜索结果
  const allSearchResults = ref([]) // 所有搜索结果
  const searchLoading = ref(false)
  const searchKeyword = ref('')
  const searchPage = ref(1)
  const searchHasMore = ref(true)
  const searchLoadingMore = ref(false)
  
  // 歌词
  const lyrics = ref([])
  const currentLyricIndex = ref(0)
  const lyricsLoading = ref(false)
  
  // 收藏列表
  const favorites = ref([])
  
  // 播放历史
  const playHistory = ref([])
  
  // Audio 实例
  const audio = ref(null)
  
  // 计算属性
  const progress = computed(() => {
    return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
  })
  
  const formattedCurrentTime = computed(() => {
    return formatTime(currentTime.value)
  })
  
  const formattedDuration = computed(() => {
    return formatTime(duration.value)
  })
  
  const hasCurrentSong = computed(() => {
    return currentSong.value !== null
  })
  
  const canPlayPrevious = computed(() => {
    return playlist.value.length > 1 && currentIndex.value > 0
  })
  
  const canPlayNext = computed(() => {
    return playlist.value.length > 1 && currentIndex.value < playlist.value.length - 1
  })
  
  // 格式化时间
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '00:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  // 初始化音频
  const initAudio = () => {
    if (!audio.value) {
      audio.value = new Audio()
      audio.value.volume = volume.value
      
      // 监听音频事件
      audio.value.addEventListener('loadstart', () => {
        isLoading.value = true
      })
      
      audio.value.addEventListener('canplay', () => {
        isLoading.value = false
        duration.value = audio.value.duration || 0
      })
      
      audio.value.addEventListener('timeupdate', () => {
        currentTime.value = audio.value.currentTime || 0
        updateCurrentLyricIndex()
      })
      
      audio.value.addEventListener('ended', () => {
        handleSongEnd()
      })
      
      audio.value.addEventListener('error', async (e) => {
        console.error('音频播放错误:', e)
        isLoading.value = false
        isPlaying.value = false

        // 如果是网络错误，尝试重新获取播放链接
        if (currentSong.value && e.target.error && e.target.error.code === 4) {
          console.log('检测到网络错误，尝试重新获取播放链接...')
          const newUrl = await getMusicUrl(currentSong.value.id)
          if (newUrl && newUrl !== audio.value.src) {
            console.log('获取到新的播放链接，重新尝试播放')
            audio.value.src = newUrl
            audio.value.load()
            setTimeout(() => {
              audio.value.play().catch(err => {
                console.error('重新播放失败:', err)
              })
            }, 1000)
          }
        }
      })
    }
  }
  
  // 播放歌曲
  const playSong = async (song) => {
    try {
      initAudio()
      
      // 如果是同一首歌，只需要播放/暂停
      if (currentSong.value && currentSong.value.id === song.id) {
        if (isPlaying.value) {
          pause()
        } else {
          resume()
        }
        return
      }
      
      // 设置新歌曲
      currentSong.value = song
      isLoading.value = true
      
      // 获取播放链接
      const playUrl = await getMusicUrl(song.id)
      if (playUrl) {
        audio.value.src = playUrl
        audio.value.load()

        // 等待音频加载完成后播放
        const playHandler = async () => {
          try {
            await audio.value.play()
            isPlaying.value = true
            isPaused.value = false
            isLoading.value = false

            // 添加到播放历史
            addToHistory(song)

            // 获取歌词
            getLyrics(song.id)
          } catch (playError) {
            console.error('播放失败:', playError)
            isLoading.value = false
            isPlaying.value = false

            // 如果播放失败，可能是链接问题，尝试重新获取
            const retryUrl = await getMusicUrl(song.id)
            if (retryUrl && retryUrl !== playUrl) {
              audio.value.src = retryUrl
              audio.value.load()
              setTimeout(() => {
                audio.value.play().catch(err => {
                  console.error('重试播放失败:', err)
                })
              }, 1000)
            }
          }
        }

        audio.value.addEventListener('canplay', playHandler, { once: true })
      } else {
        isLoading.value = false
        console.error('无法获取播放链接')
      }
    } catch (error) {
      console.error('播放歌曲失败:', error)
      isLoading.value = false
    }
  }
  
  // 暂停播放
  const pause = () => {
    if (audio.value && !audio.value.paused) {
      audio.value.pause()
      isPlaying.value = false
      isPaused.value = true
    }
  }
  
  // 恢复播放
  const resume = () => {
    if (audio.value && audio.value.paused) {
      audio.value.play()
      isPlaying.value = true
      isPaused.value = false
    }
  }
  
  // 停止播放
  const stop = () => {
    if (audio.value) {
      audio.value.pause()
      audio.value.currentTime = 0
      isPlaying.value = false
      isPaused.value = false
      currentTime.value = 0
    }
  }
  
  // 设置播放进度
  const setProgress = (percent) => {
    if (audio.value && duration.value > 0) {
      const newTime = (percent / 100) * duration.value
      audio.value.currentTime = newTime
      currentTime.value = newTime
    }
  }
  
  // 设置音量
  const setVolume = (vol) => {
    volume.value = Math.max(0, Math.min(1, vol))
    if (audio.value) {
      audio.value.volume = volume.value
    }
  }
  
  // 上一首
  const playPrevious = () => {
    if (canPlayPrevious.value) {
      currentIndex.value--
      const prevSong = playlist.value[currentIndex.value]
      if (prevSong) {
        playSong(prevSong)
      }
    }
  }
  
  // 下一首
  const playNext = () => {
    if (canPlayNext.value) {
      currentIndex.value++
      const nextSong = playlist.value[currentIndex.value]
      if (nextSong) {
        playSong(nextSong)
      }
    } else if (playMode.value === 2) {
      // 随机播放
      playRandomSong()
    }
  }
  
  // 随机播放
  const playRandomSong = () => {
    if (playlist.value.length > 1) {
      let randomIndex
      do {
        randomIndex = Math.floor(Math.random() * playlist.value.length)
      } while (randomIndex === currentIndex.value)
      
      currentIndex.value = randomIndex
      const randomSong = playlist.value[randomIndex]
      if (randomSong) {
        playSong(randomSong)
      }
    }
  }
  
  // 处理歌曲结束
  const handleSongEnd = () => {
    // 更新播放状态
    isPlaying.value = false
    isPaused.value = false

    if (playMode.value === 1) {
      // 单曲循环
      audio.value.currentTime = 0
      audio.value.play().then(() => {
        isPlaying.value = true
      }).catch(error => {
        console.error('单曲循环播放失败:', error)
      })
    } else {
      // 播放下一首
      playNext()
    }
  }
  
  // 切换播放模式
  const togglePlayMode = () => {
    playMode.value = (playMode.value + 1) % 3
  }
  
  // 设置播放列表
  const setPlaylist = (songs, currentSong) => {
    playlist.value = [...songs]
    if (currentSong) {
      const index = playlist.value.findIndex(song => song.id === currentSong.id)
      currentIndex.value = index >= 0 ? index : 0
    } else {
      currentIndex.value = 0
    }
  }

  // 添加到播放列表
  const addToPlaylist = (song) => {
    const exists = playlist.value.find(item => item.id === song.id)
    if (!exists) {
      playlist.value.push(song)
    }
  }
  
  // 从播放列表移除
  const removeFromPlaylist = (songId) => {
    const index = playlist.value.findIndex(item => item.id === songId)
    if (index > -1) {
      playlist.value.splice(index, 1)
      if (index <= currentIndex.value && currentIndex.value > 0) {
        currentIndex.value--
      }
    }
  }
  
  // 清空播放列表
  const clearPlaylist = () => {
    playlist.value = []
    currentIndex.value = 0
  }
  
  // 添加到收藏
  const addToFavorites = (song) => {
    const exists = favorites.value.find(item => item.id === song.id)
    if (!exists) {
      favorites.value.unshift(song)
      saveFavorites()
    }
  }
  
  // 从收藏移除
  const removeFromFavorites = (songId) => {
    const index = favorites.value.findIndex(item => item.id === songId)
    if (index > -1) {
      favorites.value.splice(index, 1)
      saveFavorites()
    }
  }
  
  // 检查是否已收藏
  const isFavorite = (songId) => {
    return favorites.value.some(item => item.id === songId)
  }
  
  // 添加到播放历史
  const addToHistory = (song) => {
    // 移除已存在的记录
    const existingIndex = playHistory.value.findIndex(item => item.id === song.id)
    if (existingIndex > -1) {
      playHistory.value.splice(existingIndex, 1)
    }
    
    // 添加到开头
    playHistory.value.unshift({
      ...song,
      playTime: new Date().toISOString()
    })
    
    // 限制历史记录数量
    if (playHistory.value.length > 100) {
      playHistory.value = playHistory.value.slice(0, 100)
    }
    
    saveHistory()
  }
  
  // 搜索音乐 - 一次性获取所有结果
  const searchMusic = async (keyword) => {
    searchLoading.value = true
    searchKeyword.value = keyword
    searchResults.value = []
    allSearchResults.value = []
    searchHasMore.value = false // 一次性加载完，不需要更多

    try {
      // 一次性获取更多数据，从多个页面收集
      const allResults = []
      const maxPages = 5 // 最多获取5页
      const pageSize = 20 // 每页20条

      for (let page = 1; page <= maxPages; page++) {
        try {
          const params = new URLSearchParams({
            word: keyword,
            page: page.toString(),
            num: pageSize.toString()
          })

          const response = await fetch(`https://api.vkeys.cn/v2/music/netease?${params}`, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            signal: AbortSignal.timeout(10000) // 每页10秒超时
          })

          if (!response.ok) {
            console.warn(`第${page}页请求失败: ${response.status}`)
            break
          }

          const data = await response.json()

          if (data.code === 200 && data.data) {
            let results = Array.isArray(data.data) ? data.data : [data.data]
            results = results.filter(item => item && item.id && item.song)

            if (results.length === 0) {
              break // 没有数据时停止
            }

            allResults.push(...results)

            // 如果返回的数据少于请求数量，说明没有更多页面了
            if (results.length < pageSize) {
              break
            }
          } else {
            console.warn(`第${page}页返回错误:`, data.message)
            break
          }
        } catch (pageError) {
          console.warn(`获取第${page}页数据失败:`, pageError)
          break
        }
      }

      if (allResults.length === 0) {
        searchResults.value = []
        allSearchResults.value = []
        return { success: true, data: [], total: 0 }
      }

      // 去重处理 - 基于ID去重
      const uniqueResults = []
      const seenIds = new Set()

      for (const song of allResults) {
        if (!seenIds.has(song.id)) {
          seenIds.add(song.id)
          uniqueResults.push(song)
        }
      }

      // 格式化音乐数据
      const formattedResults = uniqueResults.map(formatMusicData).filter(Boolean)

      searchResults.value = formattedResults
      allSearchResults.value = [...formattedResults]

      return {
        success: true,
        data: formattedResults,
        total: formattedResults.length
      }
    } catch (error) {
      console.error('搜索音乐失败:', error)
      searchResults.value = []
      allSearchResults.value = []

      let errorMessage = '搜索失败，请稍后重试'
      if (error.name === 'AbortError') {
        errorMessage = '请求超时，请检查网络连接'
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = '网络连接失败，请检查网络'
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, message: errorMessage }
    } finally {
      searchLoading.value = false
    }
  }

  // 加载更多搜索结果（现在已经一次性加载完所有数据，这个函数保持兼容性）
  const loadMoreSearchResults = async () => {
    // 由于已经一次性加载所有数据，直接返回成功
    return { success: true, data: [], message: '所有数据已加载完成' }
  }

  // 获取歌词
  const getLyrics = async (songId) => {
    if (!songId) return

    lyricsLoading.value = true
    lyrics.value = [] // 清空之前的歌词
    currentLyricIndex.value = 0

    try {
      const response = await fetch(`https://api.vkeys.cn/v2/music/netease/lyric?id=${songId}`)
      const data = await response.json()

      if (data.code === 200 && data.data) {
        // 尝试获取歌词，优先使用 lrc，其次使用 lyric
        const lrcText = data.data.lrc || data.data.lyric

        if (lrcText) {
          const parsedLyrics = parseLyrics(lrcText)
          if (parsedLyrics.length > 0) {
            lyrics.value = parsedLyrics
            return { success: true, data: parsedLyrics }
          }
        }

        // 如果没有歌词内容，设置为空
        lyrics.value = []
        return { success: false, message: '暂无歌词' }
      } else {
        lyrics.value = []
        return { success: false, message: data.message || '获取歌词失败' }
      }
    } catch (error) {
      console.error('获取歌词失败:', error)
      lyrics.value = []
      return { success: false, message: '网络错误，请稍后重试' }
    } finally {
      lyricsLoading.value = false
    }
  }

  // 解析LRC歌词格式
  const parseLyrics = (lrcText) => {
    if (!lrcText) return []

    const lines = lrcText.split('\n')
    const lyrics = []

    lines.forEach(line => {
      // 支持多种时间格式：[mm:ss.xx] 或 [mm:ss]
      const match = line.match(/\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\](.*)/)
      if (match) {
        const minutes = parseInt(match[1])
        const seconds = parseInt(match[2])
        const milliseconds = match[3] ? parseInt(match[3].padEnd(3, '0')) : 0
        const text = match[4].trim()

        if (text && text !== '') {
          const time = minutes * 60 + seconds + milliseconds / 1000
          lyrics.push({
            time,
            text
          })
        }
      }
    })

    // 按时间排序并去重
    const uniqueLyrics = lyrics.filter((lyric, index, arr) => {
      return index === 0 || lyric.time !== arr[index - 1].time
    })

    return uniqueLyrics.sort((a, b) => a.time - b.time)
  }

  // 更新当前歌词索引
  const updateCurrentLyricIndex = () => {
    if (lyrics.value.length === 0) return

    const currentTime = audio.value?.currentTime || 0
    let index = 0

    for (let i = 0; i < lyrics.value.length; i++) {
      if (lyrics.value[i].time <= currentTime) {
        index = i
      } else {
        break
      }
    }

    currentLyricIndex.value = index
  }

  // 获取音乐播放链接
  const getMusicUrl = async (songId, quality = 4) => {
    try {
      // 使用API模块的getMusicUrl函数
      const result = await import('@/api/music').then(module => module.getMusicUrl(songId, quality))

      if (result.success && result.url) {
        return result.url
      } else {
        console.warn('获取播放链接失败:', result.message)
        return null
      }
    } catch (error) {
      console.error('获取音乐链接失败:', error)
      return null
    }
  }
  
  // 保存收藏到本地存储
  const saveFavorites = () => {
    localStorage.setItem('music-favorites', JSON.stringify(favorites.value))
  }
  
  // 加载收藏
  const loadFavorites = () => {
    const saved = localStorage.getItem('music-favorites')
    if (saved) {
      try {
        favorites.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载收藏失败:', error)
      }
    }
  }
  
  // 保存播放历史
  const saveHistory = () => {
    localStorage.setItem('music-history', JSON.stringify(playHistory.value))
  }
  
  // 加载播放历史
  const loadHistory = () => {
    const saved = localStorage.getItem('music-history')
    if (saved) {
      try {
        playHistory.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载播放历史失败:', error)
      }
    }
  }
  
  // 初始化
  const init = () => {
    loadFavorites()
    loadHistory()
  }
  
  return {
    // 状态
    currentSong,
    isPlaying,
    isPaused,
    isLoading,
    currentTime,
    duration,
    volume,
    playMode,
    playlist,
    currentIndex,
    searchResults,
    allSearchResults,
    searchLoading,
    searchKeyword,
    searchPage,
    searchHasMore,
    searchLoadingMore,
    lyrics,
    currentLyricIndex,
    lyricsLoading,
    favorites,
    playHistory,
    
    // 计算属性
    progress,
    formattedCurrentTime,
    formattedDuration,
    hasCurrentSong,
    canPlayPrevious,
    canPlayNext,
    
    // 方法
    initAudio,
    playSong,
    pause,
    resume,
    stop,
    setProgress,
    setVolume,
    playPrevious,
    playNext,
    togglePlayMode,
    setPlaylist,
    addToPlaylist,
    removeFromPlaylist,
    clearPlaylist,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    addToHistory,
    searchMusic,
    loadMoreSearchResults,
    getLyrics,
    getMusicUrl,
    init
  }
})
