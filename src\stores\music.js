import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMusicStore = defineStore('music', () => {
  // 当前播放的歌曲
  const currentSong = ref(null)
  
  // 播放状态
  const isPlaying = ref(false)
  const isPaused = ref(false)
  const isLoading = ref(false)
  
  // 播放进度
  const currentTime = ref(0)
  const duration = ref(0)
  const volume = ref(0.8)
  
  // 播放模式 (0: 顺序播放, 1: 单曲循环, 2: 随机播放)
  const playMode = ref(0)
  
  // 播放列表
  const playlist = ref([])
  const currentIndex = ref(0)
  
  // 搜索相关
  const searchResults = ref([]) // 当前显示的搜索结果
  const allSearchResults = ref([]) // 所有搜索结果
  const searchLoading = ref(false)
  const searchKeyword = ref('')
  const searchPage = ref(1)
  const searchHasMore = ref(true)
  const searchLoadingMore = ref(false)
  
  // 歌词
  const lyrics = ref([])
  const currentLyricIndex = ref(0)
  const lyricsLoading = ref(false)
  
  // 收藏列表
  const favorites = ref([])
  
  // 播放历史
  const playHistory = ref([])
  
  // Audio 实例
  const audio = ref(null)
  
  // 计算属性
  const progress = computed(() => {
    return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
  })
  
  const formattedCurrentTime = computed(() => {
    return formatTime(currentTime.value)
  })
  
  const formattedDuration = computed(() => {
    return formatTime(duration.value)
  })
  
  const hasCurrentSong = computed(() => {
    return currentSong.value !== null
  })
  
  const canPlayPrevious = computed(() => {
    return playlist.value.length > 1 && currentIndex.value > 0
  })
  
  const canPlayNext = computed(() => {
    return playlist.value.length > 1 && currentIndex.value < playlist.value.length - 1
  })
  
  // 格式化时间
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '00:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  // 初始化音频
  const initAudio = () => {
    if (!audio.value) {
      audio.value = new Audio()
      audio.value.volume = volume.value
      
      // 监听音频事件
      audio.value.addEventListener('loadstart', () => {
        isLoading.value = true
      })
      
      audio.value.addEventListener('canplay', () => {
        isLoading.value = false
        duration.value = audio.value.duration || 0
      })
      
      audio.value.addEventListener('timeupdate', () => {
        currentTime.value = audio.value.currentTime || 0
        updateCurrentLyricIndex()
      })
      
      audio.value.addEventListener('ended', () => {
        handleSongEnd()
      })
      
      audio.value.addEventListener('error', async (e) => {
        console.error('音频播放错误:', e)
        isLoading.value = false
        isPlaying.value = false

        // 如果是网络错误，尝试重新获取播放链接
        if (currentSong.value && e.target.error && e.target.error.code === 4) {
          console.log('检测到网络错误，尝试重新获取播放链接...')
          const newUrl = await getMusicUrl(currentSong.value.id)
          if (newUrl && newUrl !== audio.value.src) {
            console.log('获取到新的播放链接，重新尝试播放')
            audio.value.src = newUrl
            audio.value.load()
            setTimeout(() => {
              audio.value.play().catch(err => {
                console.error('重新播放失败:', err)
              })
            }, 1000)
          }
        }
      })
    }
  }
  
  // 播放歌曲
  const playSong = async (song) => {
    try {
      initAudio()
      
      // 如果是同一首歌，只需要播放/暂停
      if (currentSong.value && currentSong.value.id === song.id) {
        if (isPlaying.value) {
          pause()
        } else {
          resume()
        }
        return
      }
      
      // 设置新歌曲
      currentSong.value = song
      isLoading.value = true
      
      // 获取播放链接
      const playUrl = await getMusicUrl(song.id)
      if (playUrl) {
        audio.value.src = playUrl
        audio.value.load()

        // 等待音频加载完成后播放
        const playHandler = async () => {
          try {
            await audio.value.play()
            isPlaying.value = true
            isPaused.value = false
            isLoading.value = false

            // 添加到播放历史
            addToHistory(song)

            // 获取歌词
            getLyrics(song.id)
          } catch (playError) {
            console.error('播放失败:', playError)
            isLoading.value = false
            isPlaying.value = false

            // 如果播放失败，可能是链接问题，尝试重新获取
            const retryUrl = await getMusicUrl(song.id)
            if (retryUrl && retryUrl !== playUrl) {
              audio.value.src = retryUrl
              audio.value.load()
              setTimeout(() => {
                audio.value.play().catch(err => {
                  console.error('重试播放失败:', err)
                })
              }, 1000)
            }
          }
        }

        audio.value.addEventListener('canplay', playHandler, { once: true })
      } else {
        isLoading.value = false
        console.error('无法获取播放链接')
      }
    } catch (error) {
      console.error('播放歌曲失败:', error)
      isLoading.value = false
    }
  }
  
  // 暂停播放
  const pause = () => {
    if (audio.value && !audio.value.paused) {
      audio.value.pause()
      isPlaying.value = false
      isPaused.value = true
    }
  }
  
  // 恢复播放
  const resume = () => {
    if (audio.value && audio.value.paused) {
      audio.value.play()
      isPlaying.value = true
      isPaused.value = false
    }
  }
  
  // 停止播放
  const stop = () => {
    if (audio.value) {
      audio.value.pause()
      audio.value.currentTime = 0
      isPlaying.value = false
      isPaused.value = false
      currentTime.value = 0
    }
  }
  
  // 设置播放进度
  const setProgress = (percent) => {
    if (audio.value && duration.value > 0) {
      const newTime = (percent / 100) * duration.value
      audio.value.currentTime = newTime
      currentTime.value = newTime
    }
  }
  
  // 设置音量
  const setVolume = (vol) => {
    volume.value = Math.max(0, Math.min(1, vol))
    if (audio.value) {
      audio.value.volume = volume.value
    }
  }
  
  // 上一首
  const playPrevious = () => {
    if (canPlayPrevious.value) {
      currentIndex.value--
      const prevSong = playlist.value[currentIndex.value]
      if (prevSong) {
        playSong(prevSong)
      }
    }
  }
  
  // 下一首
  const playNext = () => {
    if (canPlayNext.value) {
      currentIndex.value++
      const nextSong = playlist.value[currentIndex.value]
      if (nextSong) {
        playSong(nextSong)
      }
    } else if (playMode.value === 2) {
      // 随机播放
      playRandomSong()
    }
  }
  
  // 随机播放
  const playRandomSong = () => {
    if (playlist.value.length > 1) {
      let randomIndex
      do {
        randomIndex = Math.floor(Math.random() * playlist.value.length)
      } while (randomIndex === currentIndex.value)
      
      currentIndex.value = randomIndex
      const randomSong = playlist.value[randomIndex]
      if (randomSong) {
        playSong(randomSong)
      }
    }
  }
  
  // 处理歌曲结束
  const handleSongEnd = () => {
    // 更新播放状态
    isPlaying.value = false
    isPaused.value = false

    if (playMode.value === 1) {
      // 单曲循环
      audio.value.currentTime = 0
      audio.value.play().then(() => {
        isPlaying.value = true
      }).catch(error => {
        console.error('单曲循环播放失败:', error)
      })
    } else {
      // 播放下一首
      playNext()
    }
  }
  
  // 切换播放模式
  const togglePlayMode = () => {
    playMode.value = (playMode.value + 1) % 3
  }
  
  // 设置播放列表
  const setPlaylist = (songs, currentSong) => {
    playlist.value = [...songs]
    if (currentSong) {
      const index = playlist.value.findIndex(song => song.id === currentSong.id)
      currentIndex.value = index >= 0 ? index : 0
    } else {
      currentIndex.value = 0
    }
  }

  // 添加到播放列表
  const addToPlaylist = (song) => {
    const exists = playlist.value.find(item => item.id === song.id)
    if (!exists) {
      playlist.value.push(song)
    }
  }
  
  // 从播放列表移除
  const removeFromPlaylist = (songId) => {
    const index = playlist.value.findIndex(item => item.id === songId)
    if (index > -1) {
      playlist.value.splice(index, 1)
      if (index <= currentIndex.value && currentIndex.value > 0) {
        currentIndex.value--
      }
    }
  }
  
  // 清空播放列表
  const clearPlaylist = () => {
    playlist.value = []
    currentIndex.value = 0
  }
  
  // 添加到收藏
  const addToFavorites = (song) => {
    const exists = favorites.value.find(item => item.id === song.id)
    if (!exists) {
      favorites.value.unshift(song)
      saveFavorites()
    }
  }
  
  // 从收藏移除
  const removeFromFavorites = (songId) => {
    const index = favorites.value.findIndex(item => item.id === songId)
    if (index > -1) {
      favorites.value.splice(index, 1)
      saveFavorites()
    }
  }
  
  // 检查是否已收藏
  const isFavorite = (songId) => {
    return favorites.value.some(item => item.id === songId)
  }
  
  // 添加到播放历史
  const addToHistory = (song) => {
    // 移除已存在的记录
    const existingIndex = playHistory.value.findIndex(item => item.id === song.id)
    if (existingIndex > -1) {
      playHistory.value.splice(existingIndex, 1)
    }
    
    // 添加到开头
    playHistory.value.unshift({
      ...song,
      playTime: new Date().toISOString()
    })
    
    // 限制历史记录数量
    if (playHistory.value.length > 100) {
      playHistory.value = playHistory.value.slice(0, 100)
    }
    
    saveHistory()
  }
  
  // 搜索音乐
  const searchMusic = async (keyword) => {
    searchLoading.value = true
    searchKeyword.value = keyword
    searchResults.value = []
    allSearchResults.value = []
    searchPage.value = 1 // 重置页码
    searchHasMore.value = true

    try {
      // 首次搜索只获取第一页10条数据，快速响应
      const pageSize = 10
      const url = `https://api.vkeys.cn/v2/music/netease?word=${encodeURIComponent(keyword)}&page=1&num=${pageSize}`

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        signal: AbortSignal.timeout(10000) // 10秒超时
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.code === 200 && data.data) {
        const results = Array.isArray(data.data) ? data.data : [data.data]
        const validResults = results.filter(Boolean)

        // 存储搜索结果
        searchResults.value = validResults
        allSearchResults.value = [...validResults] // 复制一份到全部结果中

        // 判断是否还有更多数据
        // 如果返回的数据大于等于请求数量的一半，可能还有更多数据
        searchHasMore.value = validResults.length >= Math.max(1, pageSize / 2)

        return { success: true, data: validResults }
      } else if (data.code === 503) {
        // 503错误表示没有数据
        searchResults.value = []
        allSearchResults.value = []
        searchHasMore.value = false
        return { success: true, data: [] }
      } else {
        throw new Error(data.message || '搜索失败')
      }
    } catch (error) {
      console.error('搜索音乐失败:', error)
      searchResults.value = []
      allSearchResults.value = []
      searchHasMore.value = false

      // 根据错误类型返回更具体的错误信息
      let errorMessage = '网络错误，请稍后重试'
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络连接'
      } else if (error.message.includes('HTTP')) {
        errorMessage = '服务器响应异常，请稍后重试'
      } else if (error.name === 'AbortError') {
        errorMessage = '请求超时，请稍后重试'
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, message: errorMessage }
    } finally {
      searchLoading.value = false
    }
  }

  // 加载更多搜索结果
  const loadMoreSearchResults = async () => {
    if (!searchKeyword.value || !searchHasMore.value || searchLoadingMore.value) {
      return { success: true, data: [] }
    }

    searchLoadingMore.value = true

    try {
      // 获取下一页数据
      const nextPage = searchPage.value + 1
      const pageSize = 10
      const url = `https://api.vkeys.cn/v2/music/netease?word=${encodeURIComponent(searchKeyword.value)}&page=${nextPage}&num=${pageSize}`

      console.log(`加载更多: 第${nextPage}页, 当前已有${allSearchResults.value.length}条数据`)

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        signal: AbortSignal.timeout(10000) // 10秒超时
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.code === 200 && data.data) {
        const results = Array.isArray(data.data) ? data.data : [data.data]
        const validResults = results.filter(Boolean)

        console.log(`API返回${validResults.length}条数据`)

        if (validResults.length === 0) {
          // 没有更多数据了
          searchHasMore.value = false
          console.log('没有更多数据了')
          return { success: true, data: [] }
        }

        // 去重处理 - 检查新数据是否与已有数据重复
        const existingIds = new Set(allSearchResults.value.map(song => song.id))
        const newResults = validResults.filter(song => !existingIds.has(song.id))

        console.log(`去重后剩余${newResults.length}条新数据`)

        if (newResults.length === 0) {
          // 如果新数据全部重复，尝试获取下一页，但限制递归次数
          if (nextPage - searchPage.value < 3) { // 最多尝试3页
            searchPage.value = nextPage
            console.log('数据全部重复，尝试下一页')
            return await loadMoreSearchResults()
          } else {
            // 尝试次数过多，停止加载
            searchHasMore.value = false
            console.log('尝试次数过多，停止加载')
            return { success: true, data: [] }
          }
        }

        // 添加到所有结果中
        allSearchResults.value = [...allSearchResults.value, ...newResults]

        // 添加到显示结果中
        searchResults.value = [...searchResults.value, ...newResults]

        // 更新页码
        searchPage.value = nextPage

        // 判断是否还有更多数据
        // 如果返回的有效新数据少于请求数量的一半，可能没有更多数据了
        searchHasMore.value = newResults.length >= Math.max(1, pageSize / 2)

        console.log(`成功加载${newResults.length}条新数据，总计${allSearchResults.value.length}条`)

        return { success: true, data: newResults }
      } else if (data.code === 503) {
        // 503错误表示没有更多数据
        searchHasMore.value = false
        console.log('API返回503，没有更多数据')
        return { success: true, data: [] }
      } else {
        throw new Error(data.message || '加载更多失败')
      }
    } catch (error) {
      console.error('加载更多搜索结果失败:', error)

      // 根据错误类型返回更具体的错误信息
      let errorMessage = '网络错误，请稍后重试'
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络连接'
      } else if (error.message.includes('HTTP')) {
        errorMessage = '服务器响应异常，请稍后重试'
      } else if (error.name === 'AbortError') {
        errorMessage = '请求超时，请稍后重试'
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, message: errorMessage }
    } finally {
      searchLoadingMore.value = false
    }
  }

  // 获取歌词
  const getLyrics = async (songId) => {
    if (!songId) return

    lyricsLoading.value = true
    lyrics.value = [] // 清空之前的歌词
    currentLyricIndex.value = 0

    try {
      const response = await fetch(`https://api.vkeys.cn/v2/music/netease/lyric?id=${songId}`)
      const data = await response.json()

      if (data.code === 200 && data.data) {
        // 尝试获取歌词，优先使用 lrc，其次使用 lyric
        const lrcText = data.data.lrc || data.data.lyric

        if (lrcText) {
          const parsedLyrics = parseLyrics(lrcText)
          if (parsedLyrics.length > 0) {
            lyrics.value = parsedLyrics
            return { success: true, data: parsedLyrics }
          }
        }

        // 如果没有歌词内容，设置为空
        lyrics.value = []
        return { success: false, message: '暂无歌词' }
      } else {
        lyrics.value = []
        return { success: false, message: data.message || '获取歌词失败' }
      }
    } catch (error) {
      console.error('获取歌词失败:', error)
      lyrics.value = []
      return { success: false, message: '网络错误，请稍后重试' }
    } finally {
      lyricsLoading.value = false
    }
  }

  // 解析LRC歌词格式
  const parseLyrics = (lrcText) => {
    if (!lrcText) return []

    const lines = lrcText.split('\n')
    const lyrics = []

    lines.forEach(line => {
      // 支持多种时间格式：[mm:ss.xx] 或 [mm:ss]
      const match = line.match(/\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\](.*)/)
      if (match) {
        const minutes = parseInt(match[1])
        const seconds = parseInt(match[2])
        const milliseconds = match[3] ? parseInt(match[3].padEnd(3, '0')) : 0
        const text = match[4].trim()

        if (text && text !== '') {
          const time = minutes * 60 + seconds + milliseconds / 1000
          lyrics.push({
            time,
            text
          })
        }
      }
    })

    // 按时间排序并去重
    const uniqueLyrics = lyrics.filter((lyric, index, arr) => {
      return index === 0 || lyric.time !== arr[index - 1].time
    })

    return uniqueLyrics.sort((a, b) => a.time - b.time)
  }

  // 更新当前歌词索引
  const updateCurrentLyricIndex = () => {
    if (lyrics.value.length === 0) return

    const currentTime = audio.value?.currentTime || 0
    let index = 0

    for (let i = 0; i < lyrics.value.length; i++) {
      if (lyrics.value[i].time <= currentTime) {
        index = i
      } else {
        break
      }
    }

    currentLyricIndex.value = index
  }

  // 获取音乐播放链接（最高音质）
  const getMusicUrl = async (songId, quality = 9) => {
    try {
      const response = await fetch(`https://api.vkeys.cn/v2/music/netease?id=${songId}&quality=${quality}`)
      const data = await response.json()

      if (data.code === 200 && data.data && data.data.url) {
        return data.data.url
      }
      throw new Error(data.message || '获取播放链接失败')
    } catch (error) {
      console.error('获取音乐链接失败:', error)
      return null
    }
  }
  
  // 保存收藏到本地存储
  const saveFavorites = () => {
    localStorage.setItem('music-favorites', JSON.stringify(favorites.value))
  }
  
  // 加载收藏
  const loadFavorites = () => {
    const saved = localStorage.getItem('music-favorites')
    if (saved) {
      try {
        favorites.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载收藏失败:', error)
      }
    }
  }
  
  // 保存播放历史
  const saveHistory = () => {
    localStorage.setItem('music-history', JSON.stringify(playHistory.value))
  }
  
  // 加载播放历史
  const loadHistory = () => {
    const saved = localStorage.getItem('music-history')
    if (saved) {
      try {
        playHistory.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载播放历史失败:', error)
      }
    }
  }
  
  // 初始化
  const init = () => {
    loadFavorites()
    loadHistory()
  }
  
  return {
    // 状态
    currentSong,
    isPlaying,
    isPaused,
    isLoading,
    currentTime,
    duration,
    volume,
    playMode,
    playlist,
    currentIndex,
    searchResults,
    allSearchResults,
    searchLoading,
    searchKeyword,
    searchPage,
    searchHasMore,
    searchLoadingMore,
    lyrics,
    currentLyricIndex,
    lyricsLoading,
    favorites,
    playHistory,
    
    // 计算属性
    progress,
    formattedCurrentTime,
    formattedDuration,
    hasCurrentSong,
    canPlayPrevious,
    canPlayNext,
    
    // 方法
    initAudio,
    playSong,
    pause,
    resume,
    stop,
    setProgress,
    setVolume,
    playPrevious,
    playNext,
    togglePlayMode,
    setPlaylist,
    addToPlaylist,
    removeFromPlaylist,
    clearPlaylist,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    addToHistory,
    searchMusic,
    loadMoreSearchResults,
    getLyrics,
    getMusicUrl,
    init
  }
})
