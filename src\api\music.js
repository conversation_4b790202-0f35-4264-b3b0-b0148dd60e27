// 音乐API相关函数

const BASE_URL = 'https://api.vkeys.cn/v2/music/netease'

// 简单的内存缓存
const searchCache = new Map()
const CACHE_EXPIRE_TIME = 5 * 60 * 1000 // 5分钟过期

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now()
  for (const [key, value] of searchCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRE_TIME) {
      searchCache.delete(key)
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60000) // 每分钟清理一次

/**
 * 搜索音乐
 * @param {string} keyword - 搜索关键词
 * @param {number} page - 页码，默认1
 * @param {number} num - 每页数量，默认10
 * @returns {Promise<Object>} 搜索结果
 */
export const searchMusic = async (keyword, page = 1, num = 60) => {
  try {
    // 生成缓存键
    const cacheKey = `${keyword}-${page}-${num}`

    // 检查缓存
    if (searchCache.has(cacheKey)) {
      const cached = searchCache.get(cacheKey)
      if (Date.now() - cached.timestamp < CACHE_EXPIRE_TIME) {
        return cached.data
      } else {
        searchCache.delete(cacheKey)
      }
    }

    const params = new URLSearchParams({
      word: keyword,
      page: page.toString(),
      num: num.toString()
    })

    const response = await fetch(`${BASE_URL}?${params}`, {
      // 添加超时和缓存控制
      signal: AbortSignal.timeout(10000), // 10秒超时
      cache: 'default'
    })
    const data = await response.json()
    
    if (data.code === 200) {
      const result = {
        success: true,
        data: data.data,
        message: data.message
      }

      // 缓存成功的结果
      searchCache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })

      return result
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '搜索失败'
      }
    }
  } catch (error) {
    console.error('搜索音乐失败:', error)
    return {
      success: false,
      data: null,
      message: '网络错误，请稍后重试'
    }
  }
}

/**
 * 根据ID获取音乐详情和播放链接
 * @param {number} id - 音乐ID
 * @param {number} quality - 音质等级，默认9（最高）
 * @returns {Promise<Object>} 音乐详情
 */
export const getMusicById = async (id, quality = 9) => {
  try {
    const params = new URLSearchParams({
      id: id.toString(),
      quality: quality.toString()
    })

    const response = await fetch(`${BASE_URL}?${params}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      signal: AbortSignal.timeout(15000)
    })

    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.status}`)
    }

    const data = await response.json()

    if (data.code === 200 && data.data) {
      // 格式化返回的数据
      const formattedData = formatMusicData(data.data)
      return {
        success: true,
        data: formattedData,
        message: data.message || '获取成功'
      }
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '获取音乐失败'
      }
    }
  } catch (error) {
    console.error('获取音乐失败:', error)

    let errorMessage = '获取音乐失败，请稍后重试'
    if (error.name === 'AbortError') {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = '网络连接失败，请检查网络'
    } else if (error.message) {
      errorMessage = error.message
    }

    return {
      success: false,
      data: null,
      message: errorMessage
    }
  }
}

/**
 * 获取音乐播放链接（快速获取，用于播放）
 * @param {number} id - 音乐ID
 * @param {number} quality - 音质等级，默认4（320k）
 * @returns {Promise<Object>} 播放链接信息
 */
export const getMusicUrl = async (id, quality = 4) => {
  try {
    const params = new URLSearchParams({
      id: id.toString(),
      quality: quality.toString()
    })

    const response = await fetch(`${BASE_URL}?${params}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      signal: AbortSignal.timeout(10000) // 播放链接获取超时时间短一些
    })

    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.status}`)
    }

    const data = await response.json()

    if (data.code === 200 && data.data && data.data.url) {
      return {
        success: true,
        url: data.data.url,
        quality: data.data.quality,
        size: data.data.size,
        kbps: data.data.kbps
      }
    } else {
      return {
        success: false,
        message: data.message || '无法获取播放链接'
      }
    }
  } catch (error) {
    console.error('获取播放链接失败:', error)
    return {
      success: false,
      message: '获取播放链接失败'
    }
  }
}

/**
 * 获取歌词
 * @param {number} id - 音乐ID
 * @returns {Promise<Object>} 歌词数据
 */
export const getLyrics = async (id) => {
  try {
    const response = await fetch(`https://api.vkeys.cn/v2/music/netease/lyric?id=${id}`)
    const data = await response.json()
    
    if (data.code === 200) {
      return {
        success: true,
        data: data.data,
        message: data.message
      }
    } else {
      return {
        success: false,
        data: null,
        message: data.message || '获取歌词失败'
      }
    }
  } catch (error) {
    console.error('获取歌词失败:', error)
    return {
      success: false,
      data: null,
      message: '网络错误，请稍后重试'
    }
  }
}

/**
 * 解析LRC歌词格式
 * @param {string} lrcText - LRC格式歌词文本
 * @returns {Array} 解析后的歌词数组
 */
export const parseLyrics = (lrcText) => {
  if (!lrcText) return []
  
  const lines = lrcText.split('\n')
  const lyrics = []
  
  lines.forEach(line => {
    const match = line.match(/\[(\d{2}):(\d{2})\.(\d{2})\](.*)/)
    if (match) {
      const minutes = parseInt(match[1])
      const seconds = parseInt(match[2])
      const milliseconds = parseInt(match[3])
      const text = match[4].trim()
      
      if (text) {
        const time = minutes * 60 + seconds + milliseconds / 100
        lyrics.push({
          time,
          text
        })
      }
    }
  })
  
  return lyrics.sort((a, b) => a.time - b.time)
}

/**
 * 格式化时间（秒转为 mm:ss 格式）
 * @param {number|string} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'

  const totalSeconds = parseInt(seconds)
  const mins = Math.floor(totalSeconds / 60)
  const secs = totalSeconds % 60

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 格式化音乐数据
 * @param {Object} rawData - 原始音乐数据
 * @returns {Object} 格式化后的音乐数据
 */
export const formatMusicData = (rawData) => {
  if (!rawData || !rawData.id) return null

  // 默认封面图片
  const defaultCover = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMyNCAyNy41ODE3IDI3LjU4MTcgMjQgMzIgMjRDMzYuNDE4MyAyNCA0MCAyNy41ODE3IDQwIDMyQzQwIDM2LjQxODMgMzYuNDE4MyA0MCAzMiA0MEMyNy41ODE3IDQwIDI0IDM2LjQxODMgMjQgMzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='

  // 处理时长格式化
  let duration = '00:00'
  if (rawData.interval) {
    // 如果已经是格式化的时间字符串（如 "3分3秒"），转换为 mm:ss 格式
    if (rawData.interval.includes('分') && rawData.interval.includes('秒')) {
      const match = rawData.interval.match(/(\d+)分(\d+)秒/)
      if (match) {
        const mins = parseInt(match[1])
        const secs = parseInt(match[2])
        duration = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
    } else if (rawData.interval.includes(':')) {
      // 如果已经是 mm:ss 格式
      duration = rawData.interval
    }
  } else if (rawData.time && typeof rawData.time === 'number') {
    // 如果time是数字（秒数），转换为 mm:ss 格式
    duration = formatDuration(rawData.time)
  }

  return {
    id: rawData.id,
    name: rawData.song || rawData.name || '未知歌曲',
    artist: rawData.singer || rawData.artist || '未知歌手',
    album: rawData.album || '未知专辑',
    cover: rawData.cover || defaultCover,
    duration: duration,
    url: rawData.url || '', // 播放链接
    quality: rawData.quality || '标准',
    size: rawData.size || '',
    kbps: rawData.kbps || '',
    link: rawData.link || `https://music.163.com/#/song?id=${rawData.id}`, // 网易云链接
    time: rawData.time || '', // 发布时间
    // 添加一些额外的有用字段
    hasUrl: !!rawData.url, // 是否有播放链接
    qualityLevel: getQualityLevel(rawData.quality), // 音质等级数字
    formattedSize: formatFileSize(rawData.size) // 格式化文件大小
  }
}

/**
 * 获取音质等级数字
 * @param {string} qualityText - 音质文本
 * @returns {number} 音质等级
 */
const getQualityLevel = (qualityText) => {
  if (!qualityText) return 1

  const qualityMap = {
    '标准（64k）': 1,
    '标准（128k）': 2,
    'HQ极高（192k）': 3,
    'HQ极高（320k）': 4,
    'SQ无损': 5,
    '高解析度无损（Hi-Res）': 6,
    '高清臻音（Spatial Audio）': 7,
    '沉浸环绕声（Surround Audio）': 8,
    '超清母带（Master）': 9
  }

  return qualityMap[qualityText] || 1
}

/**
 * 格式化文件大小
 * @param {string} sizeText - 文件大小文本
 * @returns {string} 格式化后的文件大小
 */
const formatFileSize = (sizeText) => {
  if (!sizeText) return ''

  // 如果已经是格式化的文本，直接返回
  if (sizeText.includes('MB') || sizeText.includes('KB') || sizeText.includes('GB')) {
    return sizeText
  }

  // 如果是数字，转换为MB
  const size = parseFloat(sizeText)
  if (!isNaN(size)) {
    if (size > 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)}MB`
    } else if (size > 1024) {
      return `${(size / 1024).toFixed(2)}KB`
    } else {
      return `${size}B`
    }
  }

  return sizeText
}

/**
 * 音质选项
 */
export const QUALITY_OPTIONS = [
  { value: 1, label: '标准（64k）' },
  { value: 2, label: '标准（128k）' },
  { value: 3, label: 'HQ极高（192k）' },
  { value: 4, label: 'HQ极高（320k）' },
  { value: 5, label: 'SQ无损' },
  { value: 6, label: '高解析度无损（Hi-Res）' },
  { value: 7, label: '高清臻音（Spatial Audio）' },
  { value: 8, label: '沉浸环绕声（Surround Audio）' },
  { value: 9, label: '超清母带（Master）' }
]

/**
 * 获取音质标签
 * @param {number} quality - 音质等级
 * @returns {string} 音质标签
 */
export const getQualityLabel = (quality) => {
  const option = QUALITY_OPTIONS.find(opt => opt.value === quality)
  return option ? option.label : '未知音质'
}
